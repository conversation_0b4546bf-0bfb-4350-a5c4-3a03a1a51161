{"description": "Generated by Gemini.", "prompt": "Create a 2D cutting optimization web application for woodworking that:  \n  \n1. Allows users to manage inventory of base materials by:  \n- Defining dimensions (length and width) in specific units (mm, cm, inches)  \n- Specifying quantity of each base material  \n- Setting material properties (optional: thickness, material type)  \n  \n1. Enables users to input required pieces by for projects:\n- Define a name for the project\n- Defining dimensions for each piece  \n- Specifying quantity needed  \n- Setting grain direction requirements (optional)  \n- Adding labels/names for each piece  \n  \n3. Optimizes cutting layouts to:  \n- Minimize material waste  \n- Account for saw kerf (blade thickness)  \n- Respect grain direction constraints (if specified)  \n- Consider practical cutting sequences  \n  \n4. Visualizes the cutting plan with:  \n- Color-coded layout diagrams showing how pieces fit on base materials  \n- Clear labeling of each piece  \n- Dimensions displayed on the diagram  \n  \n5. Provides detailed cutting instructions:  \n- Step-by-step cutting sequence  \n- Measurements for each cut  \n- Exportable/printable cutting plan  \n  \n6. Technical implementation:  \n- Develop as a browser-based application using HTML, JavaScript, and TypeScript  \n- Use client-side processing (no server requirements)  \n- Identify specific libraries needed for the cutting algorithm (e.g., bin packing libraries)  \n- Ensure responsive design for desktop and tablet use  \n  \nPlease create this application with a clean, intuitive user interface that woodworkers of varying technical skill can easily use.", "requestFramePermissions": [], "name": "App"}